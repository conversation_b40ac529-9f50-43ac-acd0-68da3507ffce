<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.com.michelin.order.transaction.tunnel.database.TestDomainMapper">

    <resultMap id="BaseResultMap" type="com.michelin.order.transaction.tunnel.database.dataobject.TestDomainDataObject">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="property" property="property" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="BaseColumn">
        id,property
    </sql>

    <insert id="add">
        insert into test_domain
        (
        id, property
        ) values
        (
        #{id}, #{property}
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM test_domain
        WHERE id = #{id}
    </select>


</mapper>
