package com.michelin.order.transaction.tunnel.database.mapper;

import com.michelin.order.transaction.tunnel.database.dataobject.TestDomainDataObject;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

@Repository
public interface TestDomainMapper extends Mapper<TestDomainDataObject> {

    TestDomainDataObject queryById(@Param("id") String id);

    int add(TestDomainDataObject dataObject);
}
