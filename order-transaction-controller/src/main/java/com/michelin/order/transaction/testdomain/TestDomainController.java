package com.michelin.order.transaction.testdomain;

import com.michelin.order.transaction.message.MultiLanguageService;
import com.michelin.common.exception.CommonException;
import com.michelin.common.vo.BaseResponse;
import com.michelin.framework.utils.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/6/24 09:43
 * @description
 */
@RestController
public class TestDomainController implements TestDoaminApi{

    @Autowired
    private MultiLanguageService multiLanguageService;


    @Override
    @PostMapping("/test/create")
    public BaseResponse<String> create(@RequestBody TestCreateRequest request) throws CommonException {

        if (request.getId()==null) {
            throw new CommonException(multiLanguageService.getMessage("welcome.message", null));
        }

        TestDomain domain = SpringContextUtils.getBean(TestDomain.class);
        domain.setId(request.getId());
        domain.setProperty(request.getProperty());

        return BaseResponse.success("SUCCESS",domain.create());
    }
}
