package com.michelin.order.transaction.testdomain;

import com.michelin.order.transaction.testdomain.query.TestDomainQueryApi;
import com.michelin.order.transaction.testdomain.query.TestQueryRequest;
import com.michelin.order.transaction.testdomain.viewobject.TestViewObject;
import com.michelin.order.transaction.tunnel.database.TestDomainTunnel;
import com.michelin.order.transaction.tunnel.database.dataobject.TestDomainDataObject;
import com.michelin.common.vo.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class TestQueryController implements TestDomainQueryApi {

    @Autowired
    private TestDomainTunnel testDomainTunnel;

    @Override
    @PostMapping("/test/getTestDomainById")
    public BaseResponse<TestViewObject> getTestDomainById(@RequestBody  TestQueryRequest req) {
        try {
            TestDomainDataObject dataObject = testDomainTunnel.queryById(req.getId());
            if (dataObject == null) {
                return BaseResponse.success("无数据");
            }
            TestViewObject viewObject = new TestViewObject();
            BeanUtils.copyProperties(dataObject, viewObject);
            return BaseResponse.success("SUCCESS",viewObject);
        } catch (Exception e) {
            log.error("getTestDomainById失败", e);
            return BaseResponse.error("失败");
        }
    }
}
