# order-transaction-app
App layer. Functionality:
- [ ] Service interface
- [ ] convert model between api and domain
- [ ] light logic: such as math calculate. Should not add business logic


## Packeage and  Class Defination
### Package
- [ ] Base Package : com.michelin
- [ ] Second Package: {project name} . Such as : com.michelin.teds
- [ ] Domain Package: business domain name. Such as : com.michelin.teds.order
### Class
Separate query service and CUD service
- [ ] ***QueryService
- [ ] ***Service
