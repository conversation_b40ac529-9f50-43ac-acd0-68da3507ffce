package com.michelin.order.transaction.testdomain;

import com.michelin.order.transaction.tunnel.database.TestDomainTunnel;
import com.michelin.order.transaction.tunnel.database.dataobject.TestDomainDataObject;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Data
public class TestDomain {

    private String id;

    private String property;

    @Autowired
    private TestDomainTunnel testDomainTunnel;

    public String create() {
        TestDomainDataObject testDomainDataObject = new TestDomainDataObject();
        testDomainDataObject.setId(id);
        testDomainDataObject.setProperty(property);

        testDomainTunnel.create(testDomainDataObject);
        return testDomainDataObject.getId();
    }
}
