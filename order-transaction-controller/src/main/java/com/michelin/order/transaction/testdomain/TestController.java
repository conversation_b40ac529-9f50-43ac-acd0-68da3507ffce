package com.michelin.order.transaction.testdomain;

import com.michelin.order.transaction.message.MultiLanguageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    @Autowired
    private MultiLanguageService multiLanguageService;

    @GetMapping("/healthCheck")
    public String healthCheck() {
        System.out.println("Health check");

        return multiLanguageService.getMessage("welcome.message", null);
    }
}
