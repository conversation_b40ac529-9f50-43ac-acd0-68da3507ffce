server.port=8080
server.servlet.context-path=/octpurchase
server.tomcat.uri-encoding=UTF-8
server.tomcat.accept-count=1000
server.tomcat.threads.max=1000
server.tomcat.threads.min-spare=50
spring.application.id=99
spring.application.name=order-transaction


#--------------------------
# mybatis
mybatis-plus.configuration.map-underscore-to-camel-case=true
spring.datasource.url=********************************************************************
spring.datasource.username=postgres
spring.datasource.password=123456
spring.datasource.driver-class-name=org.postgresql.Driver

mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.type-aliases-package=com.michelin.backendsample.tunnel.database.dataobject
mybatis-plus.mapper-locations=classpath:mapper/*.xml
pagehelper.helperDialect=postgresql
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql
