package com.michelin.order.transaction.message;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2025/6/27 10:09
 * @description
 */
@Service
public class MultiLanguageService {

    @Autowired
    private MessageSource messageSource;

    @Value("${lang:US}")
    private String lang;

    public String getMessage(String code, Object[] args) {
        Locale locale = (lang != null) ? new Locale(lang) : Locale.getDefault();
        return messageSource.getMessage(code, args, locale);
    }
}
