# order-transaction-controller
Controller layer, Just connect with front-end


## Packeage and  Class Defination
### Package
- [ ] Base Package : com.michelin
- [ ] Second Package: {project name} . Such as : com.michelin.teds
- [ ] Domain Package: business domain name. Such as : com.michelin.teds.order
### Class
Separate query controller and CUD controller
- [ ] ***QueryController
- [ ] ***Controller
