# order-transaction-infrastructure
Contact with all the other systems, such as database, message queue, elasticsearch , eco systems etc. 
Should not return the mode from third-party system, just convert to the models ourselves.


## Packeage and  Class Defination
### Package
- [ ] Base Package : com.michelin
- [ ] Second Package: {project name} . Such as : com.michelin.teds
- [ ] tunnel Package: all the tunnel contact with the system out side.  Such as : com.michelin.teds.tunnel
- database : contact with db. 
- es : contact with elasticsearch
- message: contact with message queue
- eco system, such as teds,ulp
### Class
- [ ] ***Tunnel : As a client , call the data from third-party system and return the dataobject ourselves
- [ ] ***DataObject : data model
