# order-transaction-api
For api jar build which will be delivered to other eco systems.


## Packeage and  Class Defination
### Package
- [ ] Base Package : com.michelin
- [ ] Second Package: {project name} . Such as : com.michelin.teds
- [ ] Domain Package: business domain name. Such as : com.michelin.teds.order
- [ ] Sub Package: 
- clientobject: for request.   
- viewobject: for response .  
- Seperate Read and Create/Update/Delete request.  query : Read   Create/Update/Delete locate in domain package
### Class
- [ ] Create/Update/Delete data object:  ***ClientObject
- [ ] Read response data object: ***ViewObject
- [ ] RequestBody: ***Request
