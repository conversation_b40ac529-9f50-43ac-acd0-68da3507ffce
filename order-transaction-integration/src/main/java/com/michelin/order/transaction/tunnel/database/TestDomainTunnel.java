package com.michelin.order.transaction.tunnel.database;

import com.michelin.order.transaction.tunnel.database.dataobject.TestDomainDataObject;
import com.michelin.order.transaction.tunnel.database.mapper.TestDomainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class TestDomainTunnel {

    @Autowired
    private TestDomainMapper testDomainMapper;

    public int create(TestDomainDataObject dataObject) {
        return testDomainMapper.add(dataObject);
    }

    public TestDomainDataObject queryById(String id) {
        return testDomainMapper.queryById(id);
    }
}
