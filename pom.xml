<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.michelin</groupId>
        <artifactId>michelin-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>order-transaction</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>order-transaction</name>
    <modules>
        <module>order-transaction-start</module>
        <module>order-transaction-client</module>
        <module>order-transaction-controller</module>
        <module>order-transaction-app</module>
        <module>order-transaction-domain</module>
        <module>order-transaction-integration</module>
        <module>order-transaction-utils</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.51</version>
            </dependency>

            <!--Component-->
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>michelin-common</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>michelin-framework</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>michelin-mybatis-plus</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!--Project modules-->
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-start</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-controller</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.michelin</groupId>
                <artifactId>order-transaction-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- Sonar & JaCoco UT coverage -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.0.2155</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
